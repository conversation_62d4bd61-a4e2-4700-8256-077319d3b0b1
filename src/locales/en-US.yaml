pages:
  home: 'This page is home page'
  about: 'This page is about page'
navigations:
  home: 'Home'
  about: 'About'
  login: 'Login'
  logout: 'Log Out'
  idsync: 'ID Sync'
  billings: 'Billings'
  prebills: 'Pre-Bills'
  dashboard: 'Dashboard'
  pricing: 'Pricing'
  priviledges: 'Priviledges'
  subscriptionsreport: 'Subscription Report'
  tickets: 'Tickets'
  technicalinstallationorder: 'Techical Installation Order'
  users: 'Users'
  buildings: 'Buildings'
  subscriptiongroups: 'Subscription Groups'
  adminsettings: 'Admin Settings'
  plans: 'Plans'
  subscriptions: 'Subscriptions'
  subscriptionsl1: 'Subscriptions (L1)'
  ipaypayments: 'IPay Payments'
  ipaytest: 'IPay Simulations'
  paytest: 'Payment Test'
  admin: 'Admin'
  tailwindfix: 'Tailwind Fix'
  autocount: 'Autocount'
  report: 'Report'
  upcomingreport: 'Upcoming Report'
  forgotpass: 'Forgot Password'
  activeuserreport: 'Active User Report'
  faq: 'FAQ'
  settings: 'Settings'
  guides: 'Guides'
buildings:
  building: 'Building'
  title: 'Title'
  key: 'Key'
  location: 'Location'
  area: 'Area'
  homepass: 'Homepass'
  totalhomepass: 'Total Homepass'
  subscribercount: 'Subscriber Count'
  update: 'Update Building'
  create: 'Create New Building'
  rfs: 'RFS'
subscriptiongroups:
  subscriptiongroup: 'Subscription Group'
  user: 'User'
  groupcode: 'Group Code'
  actiontype: 'Action Type'
  update: 'Update Subscription Group'
  create: 'Create New Subscription Group'
  nouser: 'No User Assigned'
  selectuser: 'Select User'
  userKeySearch: 'Search for user'
report:
  building: 'Building'
  date: 'Date'
  averageresolutiontime: 'Avg Resolution Time'
  subscriptiontroubleshoot: 'Subscription Troubleshoot'
  ticketno: 'Ticket No'
  ticketcreatedat: 'Ticket Created At'
  ticketclosing: 'Ticket Closing'
  resolvingtime: 'Resolving Time'
  amountpaid: 'Amount Paid'
  deposit: 'Deposit'
  upcomingbilling: 'Upcoming Billing'
  activeuser: 'Active User'
  summarybills: 'Summary Billing'
  duebilldays: 'Bill Due Days'
  generate: 'Generate'
  subscriptionreport: 'Subscription Report'
  activationreport: 'Activation Report'
  latestbillreport: 'Latest Bill Report'
  ticketreport: 'Ticket Report'
  ticketsummaryreport: 'Ticket Summary Report'
  upcomingfreeusagereport: 'Upcoming Free Usage Report'
  salessummaryreport: 'Sales Summary Report'
  agentuserreport: 'Agent User Report'
  billbydate: 'Bill by Date'
  zerospecialratereport: 'Zero Special Rate Report'
  generatelisthints: 'Generate your report now'
  emptylist: 'Empty Report'
  id: '#ID'
  lastbilldate: 'Last Bill Date'
  name: 'Name'
  address: 'Address'
  contact: 'Contact'
  export: 'Export'
  exportcsv: 'Export CSV'
  jnxreport: 'JNX Report'
  filterbybilldate: 'Filter by bill date'
  filterbyplantype: 'Filter by plan type'
  startdate: 'Start Date'
  enddate: 'End Date'
  billno: 'Bill No'
  billdate: 'Bill Date'
  totalamount: 'Total Amount'
  total: 'Total'
  lastactdate: 'Last Bill/ Activation'
  subpayreport: 'Subscriptions Payment Report'
  billwithagent: 'Bill with Agent'
  billwithagent2: 'Bill with Agent 2'
  billbyplan: 'Bill by Plan'
  plan: 'Plan'
  count: 'Count'
  agent: 'Agent'
  price: 'Price'
  adhocreport: 'AdHoc Report 1'
  ticketsummary: 'Ticket Summary'
  invalidicuserreport: 'Invalid IC User Report'
installationorders:
  order: 'Installation Order'
  print: 'Print Installation Order'
  title2: 'Installation Order'
  address: 'Address'
  title: 'Title'
  planid: 'Plan ID'
  voip: 'VoIP'
  voipusername: 'VOIP Username'
  voipuserpassword: 'VOIP Password'
  price: 'Price'
  tax: 'Tax'
  contract: 'Contract'
  details: 'Details'
  images: 'Images'
  startdate: 'Start Date'
  enddate: 'End Date'
  agent: 'Agent'
  customer: 'Customer'
  date: 'Date'
  installer: 'Installer'
  contact: 'Contact'
  email: 'Email'
  username: 'UserName'
  userpassword: 'UserPassword'
labels:
  language: 'Languages'
c:
  searchNow: 'Search Now'
  clear: 'Clear'
  processing: 'Processing'
  uploading: 'Uploading'
  users: 'Users'
  subscriptions: 'Subscriptions'
  billnoFilter: 'Bill No Filter'
  importnow: 'Import now'
  checkall: 'Check All'
  importfromcsv: 'Import from CSV'
  filter: 'Filter'
  more: 'More'
  submitforverification: 'Submit for Verification'
  highlight: 'Highlight'
  clearfilter: 'Clear Filter'
  showfixbill: 'Fix Bill'
  showunit: 'Show Unit'
  getsubscriptions: 'Get Subscriptions List'
  activesubscriptions: 'Active Subscriptions'
  newsubscribemonth: 'Monthly New Scriptions'
  pendinginstall: 'Pending Install'
  addnew: 'Add New'
  subtotal: 'Subtotal'
  selectPayment: 'Payment Option'
  calculate: 'Calculate'
  or: 'Or'
  submit: 'Submit'
  cancel: 'Cancel'
  okay: 'Okay'
  frontPlc: 'Insert '
  action: 'Action'
  loading: 'Loading'
  admin: 'Admin'
  customer: 'Customer'
  createTitle: 'Create '
  createSuccess: 'Created successful.'
  createError: 'Create failed.'
  updateTitle: 'Update '
  updateSuccess: 'Update successful.'
  updateError: 'Update failed.'
  deleteTitle: 'Delete '
  confirmDelete: 'Are you sure delete '
  fieldRequired: 'This field cannot be empty!'
  fieldNotLessThan10: 'This field cannot less than 10 words.'
  noNameNoEmail: 'No Name No Email'
  selectDate: 'Select Date'
  selectMonth: 'Select Month'
  new: 'New'
  nocustomer: 'No Customer Attached'
  selectcustomer: 'Select A Customer'
  customerKeySearch: 'Search Customer by name, IC No, email or contact no'
  noagent: 'No Agent Attached'
  selectagent: 'Select an agent'
  agentKeySearch: 'Search agent by name, IC No, email or contact no'
  noTitle: 'No Title'
  pending: 'Pending'
  total: 'Total'
  close: 'Close'
  search: 'Search'
  yes: 'Yes'
  no: 'No'
  agent: 'Agent'
  support: 'Support'
  supportl1: 'Support L1'
  installer: 'Installer'
  all: 'All'
  male: 'Male'
  female: 'Female'
  billing: 'Billing'
  edit: 'Edit'
  payment: 'Payment'
  print: 'Print'
  back: 'Back'
  createdSuccess: 'Created Success'
  delete: 'Delete'
  reload: 'Reload'
  withAmount: 'With Amount'
  showbillno: 'Show Bill No'
  showid: 'Show ID'
  textcopied: 'Text Copied'
  resetpassword: 'Reset Password'
  forgotpassword: 'Forgot Password'
  forgotCheckMail: 'Forgot Password requested. We are sending the email.'
  forgotCheckMail2: 'Please check your email for further instructions.'
  upaylink: 'User Paylink'
  tax: 'Tax'
  amtbf: 'Amount BF'
  makepayment: 'Make Payment'
  currentmonthbill: 'Current Month Bills Paid / Total'
  totalcollected: 'Total Collected This Month'
  totalsuspended: 'Total Suspended'
errs:
  emailInvalid: 'Invalid Email'
  passInvalid: "Password doesn't match the minimum security requirement!"
  passInvalid1: 'Password must have at least 8 characters!'
  passInvalid2: 'Password must have at least 1 alphabet and 1 number!'
  loginFailed: 'Login Failed'
  loginFailedContent: 'Please try again with correct email and password pair.'
  invalidAmount: 'Invalid Amount'
  paymentdateCannotBeEmpty: 'Payment Date Cannot Be Empty'
  forgotFailed: 'Forgot Password Failed'
  forgotFailedContent: 'Email not exist.'
  voipno: 'Voip No invalid'
login:
  signin: 'Sign In'
  signup: 'Sign Up'
  orcontinuewith: 'or continue with'
  signinwith: 'Sign In with '
  tojoinus: ' to join our community'
  emailPlc: 'Email'
  passwordPlc: 'Password'
upload:
  selectFiles: Select Files
  dropfiletoupload: Drop files anywhere to upload
  or: OR
  selectfiles: Select Files
  start: Start Upload
  stop: Stop Upload
files:
  confirmDel: 'Are you sure you want delete?'
  sureDel: 'You are delete files below.'
users:
  user: 'User'
  listTitle: 'User List'
  formTitle: 'User Form'
  email: 'Email'
  identityNo: 'IC No. / Passport'
  mobile: 'Mobile'
  name: 'Full Name'
  customer: 'Customer'
  mothersName: "Mother's Name"
  fathersName: "Father's Name"
  eInvoice: "E-Invoice"
  autoCount: "Auto Count"
  scopes: 'Scopes'
  lastIP: 'Last IP'
  lastLogin: 'Last Login'
  status: 'Status'
  showSubscription: 'Show Subscriptions'
  subscriptions: 'Subscriptions'
  agent: 'Agent'
  title: 'Title'
  others: 'Others'
  dob: 'Date of Birth'
  gender: 'Gender'
  contact: 'Contact'
  contact2: 'Sub Contact'
  registerUser: 'Register User'
  nationality : 'Nationality'
  installer: 'Installer'
  support: 'Support'
  supportl1: 'Support L1'
  billings: 'Billings'
  payments: 'Payments'
  voipno: 'VOIP No'
  voipusername: 'VOIP Username'
  voipuserpassword: 'VOIP Password'
  commissionrate: 'Commission Rate'
  commissiontype: 'Commission Type'
  agentparent: 'Agent Parent'
  overwriterate: 'Overwrite Rate'
  commrepeat: 'Comm Repeat'
  parentcommrepeat: 'Parent Comm Repeat'
  msic: "MSIC Code"
billings:
  subscriptions: 'Subscriptions'
  itemsRequired: 'Billing Items Required!'
  loadSubscriptions: 'Load Subscriptions'
  amonthlater: 'A month later'
  billing: 'Billing'
  listTitle: 'Billing List'
  formTitle: 'Billing Form'
  user: 'User'
  customer: 'Customer'
  subscription: 'Subscription'
  items: 'Items'
  billdate: 'Billing date'
  duedate: 'Due date'
  amountcurrent: 'Amount Current'
  amountpaid: 'Amount Paid'
  amountbf: 'Amount Bf'
  amountcf: 'Amount Cf'
  totalamount: 'Total Amount'
  alreadycf: 'Already CF'
  status: 'Status'
  itemName: 'ItemName'
  amount: 'Amount'
  tax: 'Tax %'
  subtotal: 'Subtotal'
  billingid: 'Billing ID'
  total: 'Total'
  taxcurrent: 'Current Tax'
  id: '#ID'
  billno: 'Bill No'
  paid: 'Paid'
  synced: 'Synced'
tickets:
  ticket: 'Ticket'
  listTitle: 'Ticket List'
  formTitle: 'Ticket Form'
  createdAt: 'Created At'
  followUpFormTitle: 'Follow Up'
  update: 'Update Ticket'
  date: 'Date'
  subject: 'Subject'
  department: 'Department'
  ticketNo: 'Ticket No.'
  title: 'Title'
  description: 'Description'
  assignee: 'Assignee'
  attachments: 'Attachments'
  customer: 'Customer'
  showCustomer: 'Show Customer'
  hideCustomer: 'Hide Customer'
  closingdate: 'Closing Date'
  done: 'Done'
  status: 'Status'
  new: 'New Ticket'
  ticketEntry: 'Ticket Entry'
  ticketNoPlc: 'Auto Ticket No On Create'
  submitTicket: 'Submit Ticket'
  subscription: 'Subscription'
  subscriptionPlc: "Select user's subscription"
  level: "Level"
  levelPlc: "Select a level"
  followup: 'Follow Up'
  closetic: 'Close Ticket'
  unclosetic: 'Unclose Ticket'
  name: 'Name'
  email: 'Email'
  identityno: 'Identity No.'
  mobile: 'Mobile'
  mothersname: "Mother's Name"
  fathersname: "Father's Name"
  noAssignee: 'No Assignee'
  reply: 'Reply'
  dp_billing: 'Billing'
  dp_technical: 'Technical'
  dp_general: 'General'
plans:
  plan: 'Plan'
  hide: 'Hide'
  listTitle: 'Plan List'
  formTitle: 'Plan Form'
  planid: 'Plan ID'
  title: 'Title'
  price: 'Price'
  contract: 'Contract'
  freedays: 'Free Days'
  price2: 'Price Yr2'
  price3: 'Price Yr3'
  price4: 'Price Yr4'
  price5: 'Price Yr5'
  tax: 'Tax'
  details: 'Details'
  images: 'Images'
  startdate: 'Start Date'
  enddate: 'End Date'
  voip: 'VOIP'
subscriptions:
  ageing: 'Ageing (Days)'
  installationfee: 'Installation Fee'
  specialprice: 'Special Rate'
  sid: 'Subscription ID'
  price: 'Price'
  needToPay: 'Amount Need to Pay'
  monthlypayment: 'Monthly Payment'
  unit: 'Unit'
  level: 'Level'
  block: 'Block'
  postcode: 'Postcode'
  town: 'Town'
  state: 'State'
  prepayment: 'Pre-Payment'
  deposit: 'Deposit'
  advancedpayment: 'Advance Payment'
  contact: 'Contact'
  suggestusernameformat: 'Suggested Username'
  applicant: 'Applicant'
  applicantinfo: 'Applicant Information'
  sameasabove: 'Same as above'
  installtioninfo: 'Service Installation Details'
  newsubscriber: 'New Subscriber'
  subscription: 'Subscription'
  listTitle: 'Subscription List'
  formTitle: 'Subscription Form'
  customer: 'Customer'
  searchcustomer: 'Search Customer'
  searchcustomerhints: 'Search by IC Number, Name or mobile number'
  agent: 'Agent'
  plan: 'Plan'
  suggestusername: 'Suggest Username'
  username: 'Username'
  checkAvailability: 'Check Availbility'
  userpassword: 'User Password'
  subscribeDate: 'Subscribe Date'
  createDate: 'Create Date'
  activationDate: 'Activation Date'
  contractEndDate: 'Contract End Date'
  autocalcontract: 'Auto'
  terminationDate: 'Termination Date'
  freezestartdate: 'Freeze Start Date'
  unfreezedate: 'Unfreeze Date'
  attachments: 'Attachments'
  remark: 'Remark'
  equipments: 'Equipments'
  address: 'Subscription Address'
  billadd: 'Billing Address'
  phone: 'Phone'
  statusTxt: 'StatusTxt'
  noagent: 'No Agent Attached'
  selectagent: 'Select A Agent'
  agentKeySearch: 'Search Agent by name, IC No, email or contact no'
  name: 'Name'
  serial: 'Serial'
  brand: 'Brand'
  model: 'Model'
  type: 'Type'
  status: 'Status'
  signupNewCustomer: 'Subscribe A Customer'
  regasnewcust: 'Register a new Customer'
  new: 'New'
  pendingdeposit: 'Pending Deposit'
  pendingactivation: 'Pending Activation'
  pendingverification: 'Pending Verification' 
  pendingcompletion: 'Pending Completion'
  pendinginstall: 'Pending Installation'
  active: 'Active'
  suspended: 'Suspended'
  cancelled: 'Cancelled'
  terminated: 'Terminated'
  returnorder: 'Return Order'
  relocation: 'Relocation'
  migration: 'Migration'
  contractMonths: 'Contract Months'
  prepaymenthints1: 'Deposit is not deductable during monthly bills.'
  prepaymenthints2: 'Advance Payment are deduct during the billing.'
  autoUsername: 'Auto Username Password By Name'
  autoUsername2: 'Auto Username Password by Block Details'
  clearUsernamePassword: 'Clear'
  confirmClear: 'Are you sure to clear the username and password?'
  installationremark: 'Installation Remark'
  installerattachments: 'Installation Attachments'
  installer: 'Installer'
  noinstaller: 'No Installer'
  selectinstaller: 'Select Installer'
  installerKeySearch: 'Search for Installer'
  voip: 'VOIP'
  internet: 'Internet'
  freeusage: 'Free Usage (Day)'
  circuit: 'Circuit'
  port: 'Port'
  splitter : 'Splitter '
  building: 'Building'
  gponsn: 'GPON SN'
  macrouter: 'Mac Router'
  readingmdf: 'MDF Reading'
  readingfws: 'FWS Reading'
  saleschannel: 'Sales Channel'
  voipusername: 'VOIP Username'
  voipuserpassword: 'VOIP Password'
saleschannel:
  direct: 'Direct Sales'
  agent: 'Agent'
  dealer: 'Dealer'
payments:
  id: 'ID'
  update: 'Update Payment'
  no_payments: 'No Payments'
  viewReceipt: 'View Receipt'
  add: 'Add Payments'
  createdate: 'Create Date'
  paymentdate: 'Payment Date'
  amount: 'Amount'
  remark: 'Remark'
  create: 'Create'
  title: 'Payment'
  platform: 'Platform'
  platformid: 'Platform ID'
  dated: 'Dated'
  attachments: 'Attachments'
  billinvalid: 'Invalid Bills or Expired'
  fpxPaymentForms: 'FPX Payment for Bill'
admin:
  generateMissingBills: 'Generate Missing Bills'
  noEmails: 'No Emails'
  importsubscribers: 'Import Subscribers'
  reports: 'Report'
  jnxreport: 'JNX Report'
  devlog: 'DEV Log'
  paymentbulk: 'Bulk Payment Updates'
  emailtools: 'Email Tools'
  faq: 'FAQ'
